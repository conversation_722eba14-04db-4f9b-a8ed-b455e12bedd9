import { ChatH<PERSON>oryService } from '../../../packages/service/chat_history/chat_history'
import { ChatStateStore } from '../../../packages/service/local_cache/chat_state_store'
import { AsyncLock } from '../../../packages/model/lock/lock'
import { getState, IWorkflowState } from '../../../packages/service/llm/state'
import logger from '../../../packages/model/logger/logger'
import { MemoryStore } from '../../../packages/service/memory/memory_store'
import { YuHeNode } from './nodes/types'
import { <PERSON><PERSON><PERSON>NodeMap, NodeRouter } from './route'

import { MessageSender } from '../../../packages/service/visualized_sop/message_sender'
import { ChatDB } from '../../../packages/service/database/chat'
import { VisualizedSopProcessor } from '../../../packages/service/visualized_sop/visualized_sop_processor'
import { sendYuHeWelComeMessage } from '../client/event_handler'
import { WorkFlow } from '../../registry/workflow'
import { YuheExtractUserSlots } from '../user_slots/user_slots_extraction'
import { ChatInterruptHandler, InterruptError } from '../../../packages/service/message_handler/interrupt/interrupt_handler'
import { YuHeHumanTransfer, YuHeHumanTransferType } from '../human_transfer/human_transfer'
import { Homework1Store } from './nodes/homework1'
import { Homework2Store, HomeworkDurationUserMessageStore } from './nodes/homework2'
import { sleep } from 'openai/core'
import { VisualizedSopTasks } from '../../../packages/service/visualized_sop/visualized_sop_task_starter'


export class YuHeWorkflow extends WorkFlow {
  /**
     * 对话流程
     * @param chat_id
     * @param user_id
     * @param userMessage
     */
  public static async step(chat_id: string, user_id: string, userMessage: string) {
    try {
      userMessage = this.transferWechatEmoji(userMessage)
      await ChatHistoryService.addUserMessage(chat_id, userMessage)
      if (userMessage === '[表情]') {
        return // 表情消息 暂不做处理
      }

      if (userMessage.toLowerCase().includes('clear')) {
        await this.resetChat(chat_id, user_id)
        return
      }

      const lock = new AsyncLock()

      await lock.acquire(chat_id, async () => { // 如果有新消息，当前回复会被丢弃
        const entryNode = (await ChatStateStore.get(chat_id)).nextStage
        const state = await getState(chat_id, user_id, userMessage) // 如果有新消息，在锁释放后，当前流程会中断
        await YuHeWorkflow.run(entryNode as YuHeNode, state)
      }, { timeout: 2 * 60 * 1000 })
    } catch (e) {
      if (!(e instanceof InterruptError)) {
        try {
          if (e instanceof Error) {
            // 将错误的堆栈信息打印到自定义 logger 中
            logger.error(`Error: ${e.message}\nStack Trace: ${e.stack}`)
          } else {
            // 如果 e 不是 Error 实例，捕获当前的堆栈信息
            const stack = new Error().stack
            logger.error(`消息回复失败: ${stack}`)
          }
          await YuHeHumanTransfer.transfer(chat_id, user_id, YuHeHumanTransferType.MessageSendFailed)
        } catch (e) {
          logger.error('转人工失败', e)
        }
      }
    }
  }

  private static async run (entryNode: YuHeNode, state: IWorkflowState) {
    let node = YuHeNodeMap[entryNode]
    logger.trace({ chat_id: state.chat_id }, `初始跳转节点: ${entryNode}`)
    if (!node) {
      logger.error(`[YuheFlow] node not found: ${entryNode}`)
      return
    }

    await this.preReply(state)

    // 根据客户消息自动转移
    const autoTransferNode = await NodeRouter.route(state)
    if (autoTransferNode === YuHeNode.DummyEnd) {
      return
    }

    if (autoTransferNode && autoTransferNode !== YuHeNode.Dummy) {
      logger.trace({ chat_id: state.chat_id }, `重定向到节点: ${autoTransferNode}`)

      node = YuHeNodeMap[autoTransferNode]
      if (!node) {
        logger.error(`[YuheFlow] auto transfer node not found: ${autoTransferNode}`)
        return
      }
    }

    if (autoTransferNode !== YuHeNode.Homework1 && autoTransferNode !== YuHeNode.Homework2) { // 作业未处理完，将消息进行延迟处理，等待作业处理结束后处理
      if (Homework1Store.getUserMessages(state.chat_id).length > 0 || Homework2Store.getUserMessages(state.chat_id).length > 0) {
        HomeworkDurationUserMessageStore.addUserMessage(state.chat_id, state.userMessage)
        await sleep(3 * 60 * 1000) // 1分钟后再进行处理
        // 把当前消息挪到最后作为新消息处理，防止受到作业的影响
        const userMessages = HomeworkDurationUserMessageStore.getUserMessages(state.chat_id)
        if (userMessages.length > 0 && userMessages[userMessages.length - 1] != state.userMessage) {
          return
        }
        HomeworkDurationUserMessageStore.clearUserMessages(state.chat_id)
        setTimeout(() => {
          HomeworkDurationUserMessageStore.clearUserMessages(state.chat_id)
        }, 1000 * 60 * 3)
        for (const message of userMessages) {
          await ChatHistoryService.moveToEnd(state.chat_id, message)
        }

      }
    }

    const nextStage = await node.invoke(state)
    await ChatStateStore.update(state.chat_id, { nextStage })

    await this.postReply(state)
  }

  private static async postReply(state: IWorkflowState) {

  }

  private static transferWechatEmoji(message: string) {
    const emojiRegex = /\[.*?\]/g
    const emojiMap = {
      '[微笑]': '😊',
      '[调皮]': '😝',
      '[合十]': '🙏',
      '[爱心]': '💗',
      '[玫瑰]': '🌹',
      '[捂脸]': '🤦',
      '[笑哭]': '😂',
      '[咖啡]': '☕️',
      '[抱拳]': '🙏',
      '[拥抱]': '🫂'
    }
    return message.replace(emojiRegex, (match) => {
      const emoji = emojiMap[match]
      return emoji || match
    })
  }

  private static async preReply(state: IWorkflowState) {
    // 异步提取 Memory, 客户槽位
    MemoryStore.pushRecentMemoryToVectorDB(state.chat_id, state.round_id, new YuheExtractUserSlots())
  }

  private static async resetChat(chat_id: string, user_id: string) {
    await MessageSender.sendById({
      user_id,
      chat_id: chat_id,
      ai_msg: '聊天已重置'
    })
    await ChatHistoryService.clearChatHistory(chat_id)
    await ChatStateStore.clear(chat_id)
    if (await ChatDB.getById(chat_id)) {
      await ChatDB.setHumanInvolvement(chat_id, false)
    }

    await VisualizedSopTasks.clearSop(chat_id)

    // clear memory
    await MemoryStore.clearMemory(chat_id)

    await sendYuHeWelComeMessage(chat_id, user_id)
  }
}