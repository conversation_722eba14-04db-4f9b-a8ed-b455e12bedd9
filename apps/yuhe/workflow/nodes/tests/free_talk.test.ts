import { IWorkflowState } from '../../../../../packages/service/llm/state'
import { FreeTalkNode } from '../free_talk'
import { ChatHistoryService } from '../../../../../packages/service/chat_history/chat_history'
import { ChatInterruptHandler } from '../../../../../packages/service/message_handler/interrupt/interrupt_handler'
import { faker } from '@faker-js/faker'
import { YuheDataService } from '../../../helper/getter/get_data'
import { YuHeContextManager } from '../../context/context_manager'
import { TaskRegister } from '../../../schedule/register_task'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
  }, 60000)


  it('freeTalkTest', async () => {
    const chatId = '7881300516060552_1688857404698934'
    await mockUserData(chatId)
    const userMessage = '泥嚎'
    await ChatHistoryService.addUserMessage(chatId, userMessage)
    const dbMessages = await ChatHistoryService.getChatHistoryByChatId(chatId)

    const state: IWorkflowState = {
      chat_id: chatId,
      user_id: faker.string.uuid(),
      round_id: faker.string.uuid(),
      userMessage: userMessage,
      interruptHandler: await ChatInterruptHandler.create(chatId),
    }

    TaskRegister.register()
    YuheDataService.getCurrentTime = async () => {
      return {
        day: 1,
        time: '16:00:00',
      }
    }
    YuheDataService.isPaidSystemCourse = async () => {
      return false
    }

    await FreeTalkNode.invoke(state)
  }, 9E8)
})

export async function mockUserData(chatId: string) {
  await mockChatHistory(chatId)
  await mockUserSlot(chatId)
}

async function mockUserSlot(chatId: string) {
  const userSlot = await YuHeContextManager.getUserSlots(chatId)
  YuHeContextManager.getUserSlots = async (chatId: string) => {
    return userSlot
  }
}

async function mockChatHistory(chatId: string) {
  const fullChatHistory = await ChatHistoryService.getChatHistoryByChatId(chatId)
  ChatHistoryService.getChatHistoryByChatId = async (chat_id: string) => {
    return fullChatHistory
  }
}