import { FreeTalkNode } from '../free_talk'
import { faker } from '@faker-js/faker'
import { ChatHistoryService } from '../../../../../packages/service/chat_history/chat_history'
import { IWorkflowState } from '../../../../../packages/service/llm/state'
import { ChatInterruptHandler } from '../../../../../packages/service/message_handler/interrupt/interrupt_handler'
import { CourseTimeNode } from '../course_time'

describe('Test', function () {
  beforeAll(() => {

  })


  it('respondCourseDateTest', async () => {

    ChatHistoryService.addBotMessage = async (chat_id: string, message: string, shortDes?: string, options?: any) => {
      console.log(message)
    }

    const userMessage = '什么时候上课'
    const chat_id =  faker.string.uuid()
    await ChatHistoryService.addUserMessage(chat_id, userMessage)
    const dbMessages = await ChatHistoryService.getChatHistoryByChatId(chat_id)

    const state:IWorkflowState =  {
      chat_id,
      user_id: faker.string.uuid(),
      round_id: faker.string.uuid(),
      userMessage: userMessage,
      interruptHandler: await ChatInterruptHandler.create(chat_id)
    }

    await CourseTimeNode.invoke(state)
  }, 9E8)
})