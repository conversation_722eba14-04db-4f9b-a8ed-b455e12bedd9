import { trackInvoke, <PERSON><PERSON><PERSON>orkFlowNode } from './base_node'
import { IWorkflowState } from '../../../../packages/service/llm/state'
import { YuHeContextManager } from '../context/context_manager'
import { YuHeReply } from '../actions/reply'
import { YuHeNode } from './types'
import { YuHeThinking } from '../actions/thinking'
import { MetaActionRouter } from '../../meta_action/meta_action_router'

export class FreeTalkNode extends YuheWorkFlowNode {
  @trackInvoke
  public static async invoke(state: IWorkflowState) {
    const metaActionStage = await MetaActionRouter.getThinkAndMetaActions(state.chat_id,  state.round_id)
    const { think, actionInfo, strategy, content } = await YuHeThinking.invoke(state, metaActionStage.thinkPrompt, metaActionStage.metaActions)

    const context = await YuHeContextManager.build({
      state,
      includeRAG: true,
      includeMemory: true,
      includeUserSlots:true,
      chatHistoryRounds: 5,
      talkStrategyPrompt: `无论客户最后说什么，参考上述信息回答完客户后，都要严格执行下面的策略，要求极度精简，点到为止
${strategy}${metaActionStage.additionInfo}${actionInfo.additionInfo}`,
    })

    await YuHeReply.invoke({
      state,
      temperature: 0.8,
      max_tokens: 400,
      promptName: 'free_talk',
      context: context,
      postReplyCallBack: actionInfo.callback
    })

    return YuHeNode.FreeTalk
  }
}