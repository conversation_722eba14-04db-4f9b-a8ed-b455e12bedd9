import express from 'express'
import { XingyanPushType, IXingyanPushCallback, LiveRoomStatusChangeCallback } from '../../../packages/model/xingyan'
import logger from '../../../packages/model/logger/logger'
import { catchGlobalError } from '../../../packages/model/server/server'
import { Config } from '../../../packages/config'
import { YuHeEventForwardHandler } from './event_forward'
import { RedisDB } from '../../../packages/model/redis/redis'
import { Job, Worker } from 'bullmq'
import { YuheDataService } from '../helper/getter/get_data'
import { YuHeValidator } from '../helper/validator/validator'
import { loadConfigByWxId } from '../../../packages/model/bot_config/load_config'
import { DanmuAnalyzer } from '../danmu/danmu_analyzer'

/**
 * 进行事件转发 和 每日导入白名单事件 处理
 */
const app = express()
app.use(express.json())

app.post('/yuhe/event', async (req, res) => {
  // 接收消息
  const data: IXingyanPushCallback<any> = req.body

  res.send({
    code: 200,
    msg: 'ok'
  })

  logger.log(JSON.stringify(data, null, 4))

  // 若是直播状态事件，直接处理，不转发
  if (data.type === XingyanPushType.LIVE_ROOM_STATUS_CHANGE) {
    await handleLiveRoomStatusChange(data as LiveRoomStatusChangeCallback)
    return
  }
  YuHeEventForwardHandler.forward(data) // 转发 Yuhe 事件
})

const serverPort = 5000

catchGlobalError()


interface IYuHeLiveStreamConfigJobData {
  only_check?: boolean
  prevDay?: boolean // 前一天进行检查
}

// 启动每天导入白名单任务
new Worker('yuhe_import_white_list', async (job: Job) => {
  if (!Config.setting.wechatConfig) {
    Config.setting.wechatConfig = await loadConfigByWxId('1688858335726355') // 挂个配置，用于发送群通知消息
  }

  // 是否只做校验通知
  const data = job.data as IYuHeLiveStreamConfigJobData

  // 1. 分组配置，校验直播间配置，进阶课配置
  await YuHeValidator.validateLiveStream(data.prevDay)

  if (data.only_check) {
    return
  }

  // 2. 导入白名单
  await YuheDataService.importWhiteListOfYesterdayUsers()
}, {
  connection: RedisDB.getInstance()
})

app.listen(serverPort, '0.0.0.0', () => {
  console.log(`Server is running on port ${serverPort}`)
})

Config.setting.eventForward = true // 标记 事件转发服务

async function handleLiveRoomStatusChange(callback: LiveRoomStatusChangeCallback) {
  logger.log('直播间状态变更:', JSON.stringify(callback, null, 4))

  // 只处理直播结束 (=1) 的情况
  if (callback.param.status !== 1) return

  const roomId = callback.param.roomId.toString()
  logger.log(`直播间 ${roomId} 已结束，开始分析弹幕`)

  try {
    const analyzer = new DanmuAnalyzer()
    await analyzer.analyzeUsersByRoomId(roomId)
  } catch (error) {
    logger.warn(`直播间 ${roomId} 弹幕分析失败:`, error)
  }
}