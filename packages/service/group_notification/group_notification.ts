import { JuziAP<PERSON> } from '../../lib/juzi/api'
import { Config } from '../../config'
import { IWecomMsgType } from '../../lib/juzi/type'

/**
 * 群通知
 */
export class GroupNotification {
  public static async notify(message: string, groupId?: string) {
    await JuziAPI.sendGroupMsg(Config.setting.wechatConfig?.id as string, groupId ? groupId : Config.setting.wechatConfig?.notifyGroupId as string, {
      type: IWecomMsgType.Text,
      text: message
    })
  }
  public static async notifyToHumanGroup(message: string) {
    const HUMAN_BOT_ID = '1688858335726355'
    const HUMAN_NOTIFY_GROUP_ID = '6801c6987e27eab5358ad4c7'

    await JuziAPI.sendGroupMsg(
      HUMAN_BOT_ID,
      HUMAN_NOTIFY_GROUP_ID,
      { type: IWecomMsgType.Text, text: message }
    )
  }
}